import React, { useState, useMemo, useEffect } from 'react';
import YueModal from '@/components/YueModal/YueModal';
import { useLanguage } from '@/hooks/useLanguage';
import rectangle1 from '@/assets/images/rectangle-1.png';
import { Trans } from 'react-i18next';
import { api } from '@/services';
import { Divider } from 'antd';
import type { Genre } from '@/types/api';
import { formatNumber } from '@/utils/utils';
import FormButton from '@/pages/Register/coms/FormButton';
import tackDetailBanner from '@/assets/images/track-detail-banner.png';
import ArtistModal from '@/components/ArtistModal';
import { useNavigate } from 'react-router-dom';
interface trackDetail {
  coverArtUrl: string;
  title: string;
  artist: string;
  genre: string;
  releaseDate: string;
  upc: string;
  copyrightYear: number;
  copyrightName: string;
  phonogramCopyrightYear: number;
  phonogramCopyright: string;
  primaryGenreId: string;
  primaryGenreName?: string;
  totalRevenue?: number;
  totalStreams?: number;
  listedShares?: number;
  initialPrice?: number;
  lowestAsk?: number;
  averageAsk?: number;
  lastTransaction?: number;
}
const TrackDetailModal: React.FC<{
  id: string;
  open: boolean;
  onClose: () => void;
  onArtistModalOpen: (id: string) => void;
}> = ({ id, open, onClose, onArtistModalOpen }) => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [trackDetail, setTrackDetail] = useState<trackDetail | null>(null);
  const [genres, setGenres] = useState<Genre[]>([]);
  const fetchGenres = async () => {
    const res = await api.music.getDefaultGenres();
    if (res.code === 200) {
      setGenres(res.body);
    }
  };
  useEffect(() => {
    fetchGenres();
  }, []);

  const fetchTrackDetail = async () => {
    // const res = await api.music.getTrackDetail(id);
    // if (res.code === 200) {
    // }
    //
    setTrackDetail({
      coverArtUrl: rectangle1,
      title: '标题',
      artist: '作者',
      genre: '类型',
      releaseDate: '2025-01-01',
      upc: '000000000000',
      copyrightYear: 2025,
      copyrightName: '作品版权名称',
      phonogramCopyrightYear: 2025,
      phonogramCopyright: '录音版权名称',
      primaryGenreId: 'music.genre.pop',
      totalRevenue: 100000,
      totalStreams: 100000,
      listedShares: 100000,
      initialPrice: 100000,
      lowestAsk: 100000,
      averageAsk: 100000,
      lastTransaction: 100000,
    });
  };

  useEffect(() => {
    fetchTrackDetail();
  }, [id]);

  const showTrackDetail = useMemo(() => {
    const primaryGenreName = genres.find(
      ge => ge.code === trackDetail?.primaryGenreId
    )?.name;
    return {
      ...trackDetail,
      primaryGenreName,
      totalRevenue: formatNumber(trackDetail?.totalRevenue),
      totalStreams: formatNumber(trackDetail?.totalStreams),
      listedShares: formatNumber(trackDetail?.listedShares),
      initialPrice: formatNumber(trackDetail?.initialPrice),
      lowestAsk: formatNumber(trackDetail?.lowestAsk),
      averageAsk: formatNumber(trackDetail?.averageAsk),
      lastTransaction: formatNumber(trackDetail?.lastTransaction),
    };
  }, [trackDetail, genres]);

  const handleClose = () => {
    onClose();
  };
  return (
    <YueModal
      open={open}
      centered={false}
      onClose={handleClose}
      title={t('common.trackDetail')}
      width={1000}
      className="artist-modal [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-50px) "
    >
      <div className="pt-40px text-white text-12px   ">
        <div className="flex gap-34px mb-30px">
          <div>
            <img
              src={showTrackDetail?.coverArtUrl}
              alt="track"
              onError={e => {
                const target = e.target as HTMLImageElement;
                if (target.src !== rectangle1) {
                  target.src = rectangle1;
                }
              }}
              className="w-340px h-340px"
            />
          </div>
          <div className="pt-20px flex-1">
            <div className="text-22px font-500">{showTrackDetail?.title}</div>
            <div className="mb-10px">
              <Trans
                i18nKey="common.byTemp"
                components={[
                  <span className="ml-2px text-label" />,
                  <span
                    className="text-primary-light cursor-pointer"
                    onClick={() => {
                      onArtistModalOpen(id);
                    }}
                  />,
                ]}
                values={{
                  artist: showTrackDetail?.artist,
                }}
              />
            </div>
            <div className="mb-20px">
              <span className="text-label mr-5px">{t('common.upc')}:</span>
              <span>{showTrackDetail?.upc}</span>
            </div>
            <div className="grid grid-cols-3 gap-y-10px">
              <div>
                <span className="text-label mr-5px">
                  {t('common.releaseDate')}:
                </span>
                <span>{showTrackDetail?.releaseDate}</span>
              </div>
              <div>
                <span className="text-label ">
                  ©{showTrackDetail?.copyrightYear}{' '}
                  {showTrackDetail?.copyrightName}
                </span>
              </div>
              <div>
                <span className="text-label ">
                  ©{showTrackDetail?.phonogramCopyrightYear}{' '}
                  {showTrackDetail?.phonogramCopyright}
                </span>
              </div>
              <div>
                <span className="text-label mr-5px">{t('common.genre')}:</span>
                <span>{showTrackDetail?.primaryGenreName}</span>
              </div>
              <div>
                <span className="text-label mr-5px">
                  {t('common.revenue')}:
                </span>
                <span>{showTrackDetail?.totalRevenue}</span>
              </div>
              <div>
                <span className="text-label mr-5px">
                  {t('common.streams')}:
                </span>
                <span>{showTrackDetail?.totalStreams}</span>
              </div>
            </div>
            <Divider className="bg-[#212121] my-25px " />
            <div className="grid grid-cols-3 gap-y-10px mb-40px">
              <div>
                <span className="text-label mr-5px">
                  {t('common.listedShares')}:
                </span>
                <span>{showTrackDetail?.listedShares}</span>
              </div>
              <div>
                <span className="text-label mr-5px">
                  {t('common.initialPrice')}:
                </span>
                <span>{showTrackDetail?.initialPrice}</span>
              </div>
              <div className="col-start-1">
                <span className="text-label mr-5px">
                  {t('common.lowestAsk')}:
                </span>
                <span>{showTrackDetail?.lowestAsk}</span>
              </div>
              <div>
                <span className="text-label mr-5px">
                  {t('common.averageAsk')}:
                </span>
                <span>{showTrackDetail?.averageAsk}</span>
              </div>
              <div>
                <span className="text-label mr-5px">
                  {t('common.lastTransaction')}:
                </span>
                <span>{showTrackDetail?.lastTransaction}</span>
              </div>
            </div>
            <FormButton
              className="!w-344px"
              onClick={() => {
                // 跳转到购买页面
                navigate('/music-market/purchase');
              }}
            >
              {t('common.buyShares')}
            </FormButton>
          </div>
        </div>
        <div className="aspect-[426/187] overflow-hidden mb-40px">
          <img
            src={tackDetailBanner}
            className="w-full h-full object-cover object-top"
          />
        </div>
        <div className="text-primary-light text-14px ">
          <p className="underline">BRAINROT: HIP HOP ANIME OPENING</p>
          <p className="underline">Terlalu nyaman di pantai</p>
        </div>
      </div>
    </YueModal>
  );
};

export default TrackDetailModal;
