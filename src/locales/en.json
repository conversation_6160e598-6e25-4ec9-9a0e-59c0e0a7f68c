{"common": {"appName": "<PERSON><PERSON><PERSON><PERSON>", "back": "Back", "profile": "Profile", "slogan": "Professional Audio Resource Trading Platform", "cropAvatar": "Crop Avatar", "cancel": "Cancel", "save": "Save", "confirm": "Confirm", "close": "Close", "alias": "<PERSON><PERSON>", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "address": "Address", "phone": "Phone Number", "countryOrRegion": "Country/Region", "state": "State/Province", "city": "City", "postalZipCode": "Postal Code", "china": "China", "stageName": "Stage Name", "name": "Name", "artistBio": "Artist <PERSON><PERSON>", "changePassword": "Change Password", "saveSuccess": "Save Success", "saveFailed": "Save Failed", "emailCode": "Code", "verificationCode": "Verification Code", "resendCode": "Resend Code", "sendCode": "Send Code", "emailCodeRequired": "Please enter the email verification code", "verify": "Verify", "defaultRole": "Default Role", "artist": "Artist", "investor": "Investor", "seeAll": "See All", "weeklyStreamingChart": "Weekly Streaming Chart", "weeklyRevenueChart": "Weekly Revenue Chart", "lastUpdated": "Last Updated", "yesterday": "Yesterday", "today": "Today", "justNow": "Just now", "minutesAgo": "minutes ago", "hoursAgo": "hours ago", "daysAgo": "days ago", "title": "Title", "genre": "Genre", "revenue": "Revenue", "streams": "Streams", "submitSuccess": "Submit Success", "submitFailed": "Submit Failed", "trackDetail": "Track Detail", "releaseDate": "Release Date", "upc": "UPC", "isrc": "ISRC", "listedShares": "Listed Shares", "initialPrice": "Initial Price", "lowestAsk": "Lowest Ask", "averageAsk": "Average Ask", "lastTransaction": "Last Transaction", "buyShares": "Buy Shares", "byTemp": "<0>by</0> <1>{{artist}}</1>", "navigation": {"home": "Home", "about": "About", "profile": "Profile", "settings": "Settings", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "help": "Help", "aboutUs": "About Us", "musicMarket": "Music Marketplace", "myAssets": "My Assets", "myBalance": "My Balance", "myOrders": "My Orders", "submitMusic": "Submit Music"}, "menu": {"musicMarket": "Music Marketplace", "myAssets": "My Assets", "myBalance": "My Balance", "myOrders": "My Orders", "submitMusic": "Submit Music"}, "buttons": {"browse": "Browse Audio", "become": "Become Creator", "view": "View Collection", "backHome": "Back to Home", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "upload": "Upload", "download": "Download", "continue": "Continue", "next": "Next", "send": "Send", "sendVerificationCode": "Send Verification Code"}, "form": {"username": "Username", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "required": "Required", "invalidEmail": "Invalid email format", "passwordMismatch": "Passwords do not match", "requiredMsg": "{{label}} is required", "emailRequired": "Please enter email", "currentPasswordRequired": "Please enter current password", "currentPasswordPlaceholder": "Please enter current password", "phonePlaceholder": "Enter phone number", "phoneInvalid": "Please enter a valid phone number", "countryRegionPlaceholder": "Select country and region", "selectCountry": "Choose a country/region", "selectState": "Choose a state/province", "statePlaceholder": "Enter state/province", "enterVerificationCode": "Please enter the verification code"}, "messages": {"loading": "Loading...", "success": "Operation successful", "error": "Operation failed", "retry": "Retry", "networkError": "Network error", "serverError": "Server error", "verificationCodeSent": "Verification code sent", "emailUnavailable": "Email unavailable, already registered", "emailSameAsOld": "New email cannot be the same as the old email", "sendVerificationCodeFailed": "Failed to send verification code", "verificationSuccess": "Verification successful", "verificationCodeInvalid": "Invalid or expired verification code", "loadCountriesFailed": "Failed to load countries", "loadSubdivisionsFailed": "Failed to load states/provinces", "loadRolesFailed": "Failed to load roles", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Resource not found", "unknownError": "Unknown error", "loginExpired": "<PERSON><PERSON> expired, please login again."}, "footer": {"copyright": "<PERSON><PERSON><PERSON><PERSON> ©{{year}} Created with ❤️"}}, "auth": {"login": {"title": "User Login", "subtitle": "Sign in to your account to access more features", "form": {"username": "Username", "password": "Password", "remember": "Remember me", "submit": "<PERSON><PERSON>", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "register": "Register now", "loginWithPassword": "Login with password", "loginWithEmailCode": "Login with Email Verification Code", "resend": "Resend", "emailCodeLabel": "Please enter the verification code", "sending": "Sending...", "sendCode": "Send"}, "loginAs": "<PERSON><PERSON> as", "changeAccount": "Change Account", "codeSentTo": "Code sent to", "success": "Login successful", "error": "<PERSON><PERSON> failed, please check your username and password", "otpSent": "Verification code sent", "otpSendFailed": "Failed to send verification code, please try again"}, "register": {"title": "Signup to <PERSON><PERSON><PERSON><PERSON>", "subtitle": "Create your account to start using Yuequ", "success": "Registration successful", "error": "Registration failed, please try again", "steps": {"email": "Email Verification", "alias": "<PERSON>", "personal": "Personal Info", "password": "Set Password"}, "step1": {"title": "Email Verification", "subtitle": "Please enter your email address for verification", "hasAccount": "Already have an account?", "loginHere": "Login here", "form": {"email": "Email Address", "emailRequired": "Please enter your email", "emailInvalid": "Please enter a valid email address"}, "buttons": {"next": "Next"}, "messages": {"emailAlreadyExists": "This email is already registered, please use another email or login directly"}}, "step2": {"title": "<PERSON>", "subtitle": "This name will appear on your profile", "form": {"alias": "<PERSON><PERSON>", "aliasPlaceholder": "Enter your alias", "aliasRequired": "Please enter an alias", "aliasMaxLength": "<PERSON><PERSON> cannot exceed {{max}} characters", "aliasPattern": "Alias can only contain letters and numbers", "aliasMinLength": "Please enter at least {{min}} characters, which can be numbers or letters. Special characters are not allowed.", "aliasNoLeadingTrailingSpaces": "<PERSON><PERSON> cannot start or end with a space"}, "messages": {"aliasUnavailable": "<PERSON><PERSON> unavailable", "aliasSuggestions": "<PERSON><PERSON> already taken, suggestions: {{suggestions}}", "aliasCheckFailed": "Failed to check alias availability"}, "buttons": {"prev": "Previous", "next": "Next"}}, "step3": {"title": "Personal Information", "form": {"firstName": "First Name", "lastName": "Last Name", "phone": "Phone Number", "address": "Address", "city": "City", "country": "Country/Region", "state": "State/Province", "postalZipCode": "Postal Code", "cityPlaceholder": "Enter your city", "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "phonePlaceholder": "Enter your phone number", "addressPlaceholder": "Enter your address", "countryPlaceholder": "Enter your country", "statePlaceholder": "Enter your state/province", "postalZipCodePlaceholder": "Enter your postal code", "firstNameRequired": "Please enter your first name", "lastNameRequired": "Please enter your last name"}, "validation": {"nameLength": "Name length must be between {{min}} and {{max}} characters", "namePattern": "Name can only contain English, Chinese, spaces, and common symbols"}, "buttons": {"prev": "Previous", "next": "Next"}}, "step4": {"title": "Set Password", "subtitle": "Set a secure password for your account", "form": {"password": "Password", "confirmPassword": "Confirm Password", "passwordPlaceholder": "Enter your password", "confirmPasswordPlaceholder": "Enter your password again", "passwordRequired": "Please enter your password", "confirmPasswordRequired": "Please confirm your password", "passwordMismatch": "The passwords don't match", "passwordRequirements": {"title": "Password Requirements", "length": "At least 8 characters and include", "uppercase": "One uppercase letter", "lowercase": "One lowercase letter", "number": "One number", "special": "One special character (!@#$ ... )"}, "agreeTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "agreeTermsRequired": "Please agree to the Terms of Service and Privacy Policy", "signUp": "Sign Up"}, "buttons": {"prev": "Previous", "finish": "Complete Registration"}, "messages": {"emailVerificationRequired": "Please complete email verification first", "aliasRequired": "Please set an alias first", "personalInfoRequired": "Please complete personal information first"}}, "form": {"username": "Username", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "agreeTerms": "I have read and agree to the terms of service", "submit": "Register", "hasAccount": "Already have an account?", "login": "Login now"}}, "logout": {"success": "Successfully logged out", "confirm": "Are you sure you want to log out?"}, "profile": {"title": "User Profile", "info": "Basic Information", "security": "Security Settings", "preferences": "Preferences"}, "permission": {"denied": "You don't have permission to access this page", "login": "Please login first", "redirect": "Redirecting to login page..."}}, "home": {"title": "Welcome to Yueq<PERSON>", "subtitle": "Discover and purchase high-quality audio resources", "sections": {"audioResources": {"title": "Audio Resources", "description": "Browse and purchase various audio files including music, sound effects, voiceovers, and more.", "button": "Browse Audio"}, "creatorCenter": {"title": "Creator Center", "description": "Upload and sell your audio works to become a platform creator.", "button": "Become Creator"}, "myCollection": {"title": "My Collection", "description": "Manage your purchased audio resources and collected content.", "button": "View Collection"}}}, "about": {"title": "About Yuequ", "subtitle": "Professional Audio Resource Trading Platform", "features": {"richResources": {"title": "Rich Resources", "description": "Provides various types of high-quality audio resources to meet different needs."}, "creatorSupport": {"title": "Creator Support", "description": "Provides comprehensive upload, management and sales tools for audio creators."}, "security": {"title": "Security Guarantee", "description": "Ensures all transactions are secure and protects user rights and intellectual property."}}, "mission": {"title": "Our Mission", "description1": "<PERSON><PERSON><PERSON><PERSON> is committed to building an open, fair, and secure audio resource trading platform. We believe that every creator should be rewarded accordingly, and every user should be able to easily find the audio resources they need.", "description2": "Through advanced technology and user-friendly interfaces, we connect creators with consumers to promote the healthy development of the audio creation ecosystem."}}, "messages": {"uploadSuccess": "Upload successful", "uploadFailed": "Upload failed", "loadFailed": "Load failed", "sendFailed": "Send failed", "currentPassword": "Current Password", "loading": "Loading...", "retry": "Retry", "close": "Close", "noData": "No data available", "uploadImageFailed": "Only image files can be uploaded!", "uploadImageSizeFailed": "Image size cannot exceed 5MB!", "uploadAudioFailed": "Only audio files can be uploaded!", "uploadAudioSizeFailed": "Audio file size cannot exceed 100MB!", "uploading": "Uploading..."}, "error": {"404": {"title": "404", "subtitle": "Sorry, the page you visited does not exist.", "backHome": "Back to Home", "goBack": "Go Back"}, "500": {"title": "500", "subtitle": "Internal server error, please try again later.", "backHome": "Back to Home"}, "network": {"title": "Network Error", "subtitle": "Network connection failed, please check your network settings.", "retry": "Retry"}}, "musicMarket": {"title": "Music Marketplace", "searchPlaceholder": "Search music...", "filters": {"genreFilter": "<PERSON><PERSON>", "releasedBetween": "Released Between", "sharesAvailableOnly": "Shares Available Only", "allGenres": "All Genres"}, "table": {"title": "Title", "genre": "Genre", "revenue": "Revenue", "streams": "Streams"}, "genres": {"pop": "Pop", "jazz": "Jazz", "instrumental": "Instrumental Music", "electronic": "Electronic", "acoustic": "Acoustic"}}, "submitMusic": {"trackInfo": "Track Info", "copyrightInfo": "Copyright Info", "additionalMediaFiles": "Additional Media Files (optional)", "albumNameOptional": "Album Name (optional)", "primaryLanguage": "Primary Language", "UPCOptional": "UPC (optional)", "ISRCOptional": "ISRC (optional)", "primaryGenre": "Primary Genre", "secondaryGenre": "Secondary Genre", "originalReleaseDate": "Original Release Date", "streetDate": "Street Date (public release)", "audioFormatOptional": "Audio Format (optional)", "releaseOptions": "Release Options (optional)", "coverArtUpload": "CoverArt+Upload", "title": "Title", "labelName": "Label Name", "artistBio": "Artist <PERSON><PERSON>", "primaryArtist": "Primary Artist (Stage Name)", "trackIntro": "Track Intro (0 to 400 characters)", "trackIntro2": "Track Intro", "bidsBetween": "Bids Between", "year": "Year", "name": "Name", "supportsDolbyAtmos": "Supports Dolby Atmos", "enableiTunesPreOrder": "Enable iTunes Pre-Order", "liveConcertRecording": "Live Concert Recording", "remasteredRecording": "Remastered Recording", "upload": "Upload", "selectLanguage": "Select language", "albumCover": "Album Cover", "cropAlbumCover": "Crop Album Cover", "imageSizeTooLarge": "Image size cannot exceed 10MB", "placeholders": {"title": "Please enter the music title", "labelName": "Please enter the label name", "albumName": "Please enter the album name", "name": "Please enter the name", "datePicker": "25/11/2022", "trackIntro": "Please enter the music intro", "upc": "Please enter the UPC", "isrc": "Please enter the ISRC", "coverArtRequired": "Album cover is required"}, "languages": {"english": "English", "chinese": "中文", "japanese": "日本語"}, "characterCount": "{{current}} to {{max}} characters", "form": {"submit": "Confirm", "cancel": "Cancel"}}, "artistModal": {"title": "Artist Details", "artistProfile": "Artist Profile", "releasedTracks": "Released Tracks", "released": "Released", "revenue": "Revenue", "streams": "Streams", "artistBio": "Artist <PERSON><PERSON>", "loading": "Loading...", "noTracksFound": "No tracks found"}}